//
//  iCloudSyncTests.swift
//  ztt2Tests
//
//  Created by Augment Agent on 2025/8/3.
//

import XCTest
import CoreData
@testable import ztt2

/**
 * iCloud同步功能测试
 * 测试容器切换时的数据一致性
 */
class iCloudSyncTests: XCTestCase {
    
    var persistenceController: PersistenceController!
    var dataManager: DataManager!
    
    override func setUp() {
        super.setUp()
        
        // 使用内存存储进行测试
        persistenceController = PersistenceController(inMemory: true)
        dataManager = DataManager.shared
        
        // 创建测试数据
        createTestData()
    }
    
    override func tearDown() {
        persistenceController = nil
        dataManager = nil
        super.tearDown()
    }
    
    /**
     * 创建测试数据
     */
    private func createTestData() {
        let context = persistenceController.container.viewContext
        
        // 创建测试用户
        let user = User(context: context)
        user.id = UUID()
        user.nickname = "测试用户"
        user.appleUserID = "test_user_123"
        user.createdAt = Date()
        
        // 创建测试成员
        let member1 = Member(context: context)
        member1.id = UUID()
        member1.name = "小明"
        member1.role = "son"
        member1.currentPoints = 100
        member1.memberNumber = 1
        member1.createdAt = Date()
        member1.user = user
        
        let member2 = Member(context: context)
        member2.id = UUID()
        member2.name = "小红"
        member2.role = "daughter"
        member2.currentPoints = 80
        member2.memberNumber = 2
        member2.createdAt = Date()
        member2.user = user
        
        // 保存数据
        try? context.save()
    }
    
    /**
     * 测试关闭iCloud同步后数据是否保持可见
     */
    func testDataVisibilityAfterDisablingSync() {
        let expectation = XCTestExpectation(description: "数据在关闭同步后保持可见")
        
        // 1. 启用iCloud同步
        Task {
            let enableSuccess = await persistenceController.enableCloudKitSync()
            XCTAssertTrue(enableSuccess, "启用iCloud同步应该成功")
            
            // 2. 验证数据存在
            let membersBeforeDisable = dataManager.members
            XCTAssertEqual(membersBeforeDisable.count, 2, "启用同步后应该有2个成员")
            
            // 3. 关闭iCloud同步
            let disableSuccess = await persistenceController.disableCloudKitSync()
            XCTAssertTrue(disableSuccess, "关闭iCloud同步应该成功")
            
            // 4. 等待容器切换完成
            try? await Task.sleep(nanoseconds: 2_000_000_000) // 2秒
            
            // 5. 验证数据仍然存在
            dataManager.checkAndRefreshData()
            
            // 等待数据刷新完成
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                let membersAfterDisable = self.dataManager.members
                XCTAssertEqual(membersAfterDisable.count, 2, "关闭同步后应该仍有2个成员")
                XCTAssertEqual(membersAfterDisable[0].name, "小明", "成员数据应该保持不变")
                XCTAssertEqual(membersAfterDisable[1].name, "小红", "成员数据应该保持不变")
                
                expectation.fulfill()
            }
        }
        
        wait(for: [expectation], timeout: 10.0)
    }
    
    /**
     * 测试容器切换通知机制
     */
    func testContainerSwitchNotification() {
        let expectation = XCTestExpectation(description: "容器切换通知应该被发送")
        
        // 监听容器切换通知
        let notificationCenter = NotificationCenter.default
        let observer = notificationCenter.addObserver(
            forName: NSNotification.Name("PersistenceContainerDidSwitch"),
            object: nil,
            queue: .main
        ) { notification in
            if let userInfo = notification.userInfo,
               let containerType = userInfo["containerType"] as? String {
                XCTAssertEqual(containerType, "local", "容器类型应该是local")
                expectation.fulfill()
            }
        }
        
        // 执行容器切换
        Task {
            await persistenceController.disableCloudKitSync()
        }
        
        wait(for: [expectation], timeout: 5.0)
        notificationCenter.removeObserver(observer)
    }
    
    /**
     * 测试数据管理器的容器切换处理
     */
    func testDataManagerContainerSwitchHandling() {
        let expectation = XCTestExpectation(description: "DataManager应该正确处理容器切换")
        
        // 监听数据更新通知
        let notificationCenter = NotificationCenter.default
        let observer = notificationCenter.addObserver(
            forName: NSNotification.Name("DataManagerDidUpdateFromRemote"),
            object: nil,
            queue: .main
        ) { notification in
            if let userInfo = notification.userInfo,
               let reason = userInfo["reason"] as? String,
               reason == "container_switch" {
                expectation.fulfill()
            }
        }
        
        // 执行容器切换
        Task {
            await persistenceController.disableCloudKitSync()
        }
        
        wait(for: [expectation], timeout: 8.0)
        notificationCenter.removeObserver(observer)
    }
}
